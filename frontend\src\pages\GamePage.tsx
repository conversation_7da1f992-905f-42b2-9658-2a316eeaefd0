import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useGameStore } from '../store/gameStore';
import { Character } from '../types/gamestate';
import { LoadingScreen } from '../components/game/LoadingScreen';
import { StoryPanel } from '../components/game/StoryPanel';
import { CreatorPanel } from '../components/game/CreatorPanel';
import { GameHeader } from '../components/game/GameHeader';
import { GroupChatPanel } from '../components/game/GroupChatPanel';

export const GamePage: React.FC = () => {
  const { gameId, sessionId } = useParams<{ gameId: string; sessionId: string }>();
  const navigate = useNavigate();
  const { 
    gameState,
    initialLoading,
    error,
    loadGameSession, 
    getStoredIds,
    currentCharacterId,
    updateNovelLocation // Destructure the new action
  } = useGameStore();

  const [showChatPanel, setShowChatPanel] = useState<boolean>(false);
  const [currentCharacter, setCurrentCharacter] = useState<Character | null>(null);

  // 添加日志，帮助追踪状态
  useEffect(() => {
    console.log('[DEBUG] GamePage: initialLoading =', initialLoading);
    console.log('[DEBUG] GamePage: gameState =', gameState ? '已加载' : '未加载');
  }, [initialLoading, gameState]);

  useEffect(() => {
    const loadSession = async () => {
      if (gameId && sessionId) {
        // 优先使用URL中的sessionId，只有当加载失败时才考虑localStorage中的备用sessionId
        console.log(`Loading game session: ${gameId}/${sessionId}`);

        try {
          await loadGameSession(gameId, sessionId);
        } catch (error) {
          console.error(`Failed to load session ${sessionId}, checking localStorage backup...`);

          // 如果URL中的session加载失败，尝试使用localStorage中的备用session
          const { gameId: storedGameId, sessionId: storedSessionId } = getStoredIds();

          if (storedSessionId && storedSessionId !== sessionId) {
            console.log(`尝试重定向到存储的sessionId: ${storedSessionId}`);
            navigate(`/game/${gameId}/${storedSessionId}`);
            return;
          }

          // 如果都失败了，显示错误
          console.error('No valid session found');
        }
      } else {
        navigate('/');
      }
    };

    loadSession();
  }, [gameId, sessionId, loadGameSession, navigate, getStoredIds]);

  // 当 currentCharacterId 变化时更新当前角色
  useEffect(() => {
    if (gameState && currentCharacterId) {
      const character = gameState.characters.find(c => c.id === currentCharacterId);
      setCurrentCharacter(character || null);
      setShowChatPanel(!!character);
    } else {
      setCurrentCharacter(null);
      setShowChatPanel(false);
    }
  }, [gameState, currentCharacterId]);

  // 错误处理
  if (error) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <div className="p-6 bg-red-500/20 border border-red-500 rounded-lg text-red-400 mb-6">
            {error}
          </div>
          <button
            onClick={() => navigate('/')}
            className="button-cyber"
          >
            返回主页
          </button>
        </div>
      </div>
    );
  }

  // 加载状态 - 只有在首次加载游戏状态时显示加载页面，交互中不显示
  if (!gameState) {
    console.log('[DEBUG] GamePage: 显示加载页面，gameState未加载');
    return <LoadingScreen />;
  }

  const handleNovelLocationChange = (
    chapterIdValue: string,
    sectionIdValue: string,
    chapterTitle?: string,
    sectionTitle?: string
  ) => {
    if (gameId && sessionId) {
      console.log(
        `[DEBUG] GamePage: handleNovelLocationChange called with:
        Chapter ID: ${chapterIdValue}, Section ID: ${sectionIdValue},
        Chapter Title: ${chapterTitle}, Section Title: ${sectionTitle}`
      );
      // Call the action from the store
      updateNovelLocation(gameId, sessionId, chapterIdValue, sectionIdValue, chapterTitle, sectionTitle);
    } else {
      console.error("[ERROR] GamePage: gameId or sessionId is missing, cannot update novel location.");
    }
  };

  return (
    <div className="h-screen flex flex-col overflow-hidden">
      {/* 游戏头部 */}
      <div className="flex-shrink-0">
        <GameHeader
          gameState={gameState}
          gameId={gameId!}
          sessionId={sessionId!}
        />
      </div>

      {/* 主要游戏界面 */}
      <div className="flex-1 flex min-h-0 overflow-hidden">
        {/* 左侧：故事面板 (2/3) */}
        <div className="flex-1 min-h-0 overflow-hidden" style={{ flex: '2' }}>
          <StoryPanel
            gameState={gameState}
            gameId={gameId!}
            sessionId={sessionId!}
            onNovelLocationChange={handleNovelLocationChange}
          />
        </div>

        {/* 右侧：创作者面板 (1/3) */}
        <div className="border-l border-gray-700 min-h-0 overflow-hidden" style={{ flex: '1' }}>
          <CreatorPanel
            gameState={gameState}
            gameId={gameId!}
            sessionId={sessionId!}
          />
        </div>
      </div>

      {/* 右侧聊天面板 */}
      {showChatPanel && currentCharacter && gameId && sessionId && (
        <GroupChatPanel
          gameState={gameState}
          currentCharacter={currentCharacter}
          gameId={gameId}
          sessionId={sessionId}
        />
      )}
    </div>
  );
};