import React, { useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useGameStore } from '../store/gameStore';
import { LoadingScreen } from '../components/game/LoadingScreen';
import { CharacterAttributePanel } from '../components/game/CharacterAttributePanel';
import { CharacterCGPanel } from '../components/game/CharacterCGPanel';
import { GroupChatPanel } from '../components/game/GroupChatPanel';
import { GameHeader } from '../components/game/GameHeader';

export const CharacterPage: React.FC = () => {
  const { gameId, sessionId, characterId } = useParams<{ 
    gameId: string; 
    sessionId: string; 
    characterId: string; 
  }>();
  const navigate = useNavigate();
  const { gameState, isLoading, error, loadGameSession, setCurrentCharacterId, currentCharacterId } = useGameStore();



  // 使用ref来追踪是否已经加载过
  const loadedRef = React.useRef<string | null>(null);

  useEffect(() => {
    const sessionKey = `${gameId}-${sessionId}`;

    if (gameId && sessionId && loadedRef.current !== sessionKey) {
      loadGameSession(gameId, sessionId);
      loadedRef.current = sessionKey;
    } else if (!gameId || !sessionId) {
      navigate('/');
    }
  }, [gameId, sessionId]);

  // 当characterId变化时，更新gameStore中的当前角色ID
  useEffect(() => {
    if (characterId) {
      setCurrentCharacterId(characterId);
    }
  }, [characterId]);

  // 错误处理
  if (error) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="p-6 bg-red-500/20 border border-red-500 rounded-lg text-red-400 mb-6">
            {error}
          </div>
          <button
            onClick={() => navigate('/')}
            className="button-cyber"
          >
            返回主页
          </button>
        </div>
      </div>
    );
  }

  // 加载状态
  if (isLoading || !gameState) {
    return <LoadingScreen />;
  }

  // 查找当前角色
  const currentCharacter = gameState.characters.find(char => char.id === characterId);

  if (!currentCharacter) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="p-6 bg-red-500/20 border border-red-500 rounded-lg text-red-400 mb-6">
            角色不存在或已被删除
          </div>
          <button
            onClick={() => navigate(`/game/${gameId}/${sessionId}`)}
            className="button-cyber"
          >
            返回游戏
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col overflow-hidden">
      {/* 游戏头部 - 固定高度 */}
      <div className="flex-shrink-0">
        <GameHeader 
          gameState={gameState}
          gameId={gameId!}
          sessionId={sessionId!}
          currentCharacter={currentCharacter}
        />
      </div>
      
      {/* 主要角色界面 - 三等分布局，剩余空间 */}
      <div className="flex-1 flex overflow-hidden min-h-0">
        {/* 左侧：属性区域 (1/3) */}
        <div className="w-1/3 border-r border-gray-700 flex flex-col overflow-hidden">
          <CharacterAttributePanel 
            character={currentCharacter}
            gameState={gameState}
            gameId={gameId!}
            sessionId={sessionId!}
          />
        </div>
        
        {/* 中间：CG区域 (1/3) */}
        <div className="w-1/3 border-r border-gray-700 flex flex-col overflow-hidden">
          <CharacterCGPanel 
            character={currentCharacter}
            gameId={gameId!}
            sessionId={sessionId!}
          />
        </div>
        
        {/* 右侧：聊天区域 (1/3) */}
        <div className="w-1/3 flex flex-col min-h-0 h-full overflow-hidden">
          <GroupChatPanel 
            gameState={gameState}
            currentCharacter={currentCharacter}
            gameId={gameId!}
            sessionId={sessionId!}
          />
        </div>
      </div>
    </div>
  );
};